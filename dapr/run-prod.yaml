# Dapr Multi-App Run Configuration for Production Environment
# 使用命令: dapr run -f dapr/run-prod.yaml

version: 1
common:
  resourcesPath: ./dapr/components
  configFilePath: ./dapr/config.yaml
  
apps:
  # 爬虫任务管理服务 - 生产环境
  - appID: crawler-task-manager
    appDirPath: ./
    appPort: 8080
    command: ["./bin/crawler-task-manager"]  # 使用编译后的二进制文件
    env:
      # 服务器配置
      SERVER_PORT: "8080"
      SERVER_HOST: "0.0.0.0"
      
      # Dapr配置
      DAPR_APP_ID: "crawler-task-manager"
      DAPR_APP_PORT: "8080"
      DAPR_STATE_STORE: "statestore"
      DAPR_PUBSUB_NAME: "pubsub"
      DAPR_SECRET_STORE: "secretstore"
      
      # 生产环境配置
      GIN_MODE: "release"
      LOG_LEVEL: "info"
      
    # Dapr sidecar配置
    daprHTTPPort: 3500
    daprGRPCPort: 50001
    daprHTTPMaxRequestSize: 8
    daprHTTPReadBufferSize: 8
    enableProfiling: false
    logLevel: warn
    appLogDestination: file
    daprdLogDestination: file
    
    # 健康检查配置
    appHealthCheckPath: "/health"
    appHealthProbeInterval: 10
    appHealthProbeTimeout: 5
    appHealthThreshold: 3
