# Dapr 配置文件说明

本目录包含了爬虫任务管理服务的Dapr配置文件，支持使用 `dapr run -f` 命令启动服务。

## 配置文件说明

### 1. run-config.yaml
基础配置文件，包含最基本的服务启动配置。

### 2. run-dev.yaml
开发环境配置文件，包含：
- 调试模式启用
- 详细日志输出
- 性能分析启用
- 较短的健康检查间隔

### 3. run-prod.yaml
生产环境配置文件，包含：
- 发布模式
- 优化的日志级别
- 性能分析禁用
- 使用编译后的二进制文件

## 使用方法

### 前置条件
1. 确保Redis已启动：
   ```bash
   make redis-start
   ```

2. 安装项目依赖：
   ```bash
   make deps
   ```

### 启动服务

#### 开发环境（推荐）
```bash
# 使用开发环境配置
make dapr-run-dev

# 或直接使用dapr命令
dapr run -f dapr/run-dev.yaml
```

#### 生产环境
```bash
# 先构建应用
make build

# 使用生产环境配置
make dapr-run-prod

# 或直接使用dapr命令
dapr run -f dapr/run-prod.yaml
```

#### 简单配置
```bash
# 使用基础配置
make dapr-run-simple

# 或直接使用dapr命令
dapr run -f dapr/run-config.yaml
```

### 停止服务
```bash
# 停止所有Dapr应用
dapr stop crawler-task-manager

# 或使用Makefile
make dapr-stop
```

## 配置参数说明

### 通用配置
- `version`: 配置文件版本
- `resourcesPath`: Dapr组件配置路径
- `configFilePath`: Dapr配置文件路径

### 应用配置
- `appID`: 应用唯一标识符
- `appDirPath`: 应用工作目录
- `appPort`: 应用监听端口
- `command`: 启动命令
- `env`: 环境变量
- `daprHTTPPort`: Dapr HTTP端口
- `daprGRPCPort`: Dapr gRPC端口

### 健康检查配置
- `appHealthCheckPath`: 健康检查路径
- `appHealthProbeInterval`: 检查间隔（秒）
- `appHealthProbeTimeout`: 检查超时（秒）
- `appHealthThreshold`: 失败阈值

## 环境变量说明

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| SERVER_PORT | 服务器端口 | 8080 |
| SERVER_HOST | 服务器主机 | 0.0.0.0 |
| DAPR_APP_ID | Dapr应用ID | crawler-task-manager |
| DAPR_STATE_STORE | 状态存储名称 | statestore |
| DAPR_PUBSUB_NAME | 发布订阅名称 | pubsub |
| DAPR_SECRET_STORE | 密钥存储名称 | secretstore |
| GIN_MODE | Gin运行模式 | debug/release |
| LOG_LEVEL | 日志级别 | debug/info |

## 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :8080
   lsof -i :3500
   ```

2. **Redis连接失败**
   ```bash
   # 检查Redis状态
   docker ps | grep redis
   docker logs redis-crawler
   ```

3. **组件加载失败**
   ```bash
   # 检查组件配置
   ls -la dapr/components/
   ```

### 日志查看
```bash
# 查看应用日志
dapr logs --app-id crawler-task-manager

# 实时查看日志
dapr logs --app-id crawler-task-manager -f
```

## 自定义配置

您可以根据需要修改配置文件中的参数，例如：
- 修改端口号
- 调整健康检查参数
- 添加新的环境变量
- 修改日志级别

修改后重新启动服务即可生效。
